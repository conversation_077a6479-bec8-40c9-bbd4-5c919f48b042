// Test file để kiểm tra Statistics API
import { 
  getDepartmentStats, 
  getUserStats, 
  getProjectsByDepartment, 
  getAllDepartmentsProjects,
  getStaffTaskAssignments,
  getStaffDashboard,
  getSystemStats,
  getAdminDashboard
} from './api/statistics.js';

// Test function để kiểm tra tất cả API endpoints
export const testStatisticsAPI = async () => {
  console.log('🧪 Testing Statistics API...');
  
  const results = {
    departmentStats: null,
    userStats: null,
    projectsByDepartment: null,
    allDepartmentsProjects: null,
    staffTaskAssignments: null,
    staffDashboard: null,
    systemStats: null,
    adminDashboard: null,
    errors: []
  };

  // Test Department Stats
  try {
    console.log('📊 Testing getDepartmentStats...');
    results.departmentStats = await getDepartmentStats();
    console.log('✅ getDepartmentStats success:', results.departmentStats);
  } catch (error) {
    console.error('❌ getDepartmentStats failed:', error.message);
    results.errors.push({ endpoint: 'getDepartmentStats', error: error.message });
  }

  // Test User Stats
  try {
    console.log('👥 Testing getUserStats...');
    results.userStats = await getUserStats();
    console.log('✅ getUserStats success:', results.userStats);
  } catch (error) {
    console.error('❌ getUserStats failed:', error.message);
    results.errors.push({ endpoint: 'getUserStats', error: error.message });
  }

  // Test Projects by Department
  try {
    console.log('📁 Testing getProjectsByDepartment...');
    results.projectsByDepartment = await getProjectsByDepartment();
    console.log('✅ getProjectsByDepartment success:', results.projectsByDepartment);
  } catch (error) {
    console.error('❌ getProjectsByDepartment failed:', error.message);
    results.errors.push({ endpoint: 'getProjectsByDepartment', error: error.message });
  }

  // Test All Departments Projects
  try {
    console.log('🏢 Testing getAllDepartmentsProjects...');
    results.allDepartmentsProjects = await getAllDepartmentsProjects();
    console.log('✅ getAllDepartmentsProjects success:', results.allDepartmentsProjects);
  } catch (error) {
    console.error('❌ getAllDepartmentsProjects failed:', error.message);
    results.errors.push({ endpoint: 'getAllDepartmentsProjects', error: error.message });
  }

  // Test Staff Task Assignments
  try {
    console.log('📋 Testing getStaffTaskAssignments...');
    results.staffTaskAssignments = await getStaffTaskAssignments();
    console.log('✅ getStaffTaskAssignments success:', results.staffTaskAssignments);
  } catch (error) {
    console.error('❌ getStaffTaskAssignments failed:', error.message);
    results.errors.push({ endpoint: 'getStaffTaskAssignments', error: error.message });
  }

  // Test Staff Dashboard
  try {
    console.log('📈 Testing getStaffDashboard...');
    results.staffDashboard = await getStaffDashboard();
    console.log('✅ getStaffDashboard success:', results.staffDashboard);
  } catch (error) {
    console.error('❌ getStaffDashboard failed:', error.message);
    results.errors.push({ endpoint: 'getStaffDashboard', error: error.message });
  }

  // Test System Stats (Admin only)
  try {
    console.log('⚙️ Testing getSystemStats...');
    results.systemStats = await getSystemStats();
    console.log('✅ getSystemStats success:', results.systemStats);
  } catch (error) {
    console.error('❌ getSystemStats failed:', error.message);
    results.errors.push({ endpoint: 'getSystemStats', error: error.message });
  }

  // Test Admin Dashboard (Admin only)
  try {
    console.log('🔧 Testing getAdminDashboard...');
    results.adminDashboard = await getAdminDashboard();
    console.log('✅ getAdminDashboard success:', results.adminDashboard);
  } catch (error) {
    console.error('❌ getAdminDashboard failed:', error.message);
    results.errors.push({ endpoint: 'getAdminDashboard', error: error.message });
  }

  // Summary
  console.log('\n📋 Test Summary:');
  console.log(`✅ Successful: ${8 - results.errors.length}/8`);
  console.log(`❌ Failed: ${results.errors.length}/8`);
  
  if (results.errors.length > 0) {
    console.log('\n❌ Errors:');
    results.errors.forEach(({ endpoint, error }) => {
      console.log(`  - ${endpoint}: ${error}`);
    });
  }

  return results;
};

// Hàm để test trong browser console
window.testStatisticsAPI = testStatisticsAPI;

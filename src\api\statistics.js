import { STATISTICS_ENDPOINTS, ADMIN_ENDPOINTS } from './endpoints.js';

// Helper function để lấy token từ localStorage
const getAuthToken = () => {
  try {
    const tokenFromStorage = localStorage.getItem('token');
    const authToken = localStorage.getItem('authToken');
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userToken = user.token;

    return tokenFromStorage || authToken || userToken;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

// Helper function để tạo headers với authorization
const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Helper function để xử lý response
const handleResponse = async (response) => {
  if (!response.ok) {
    let error = { message: 'Có lỗi xảy ra' };
    try {
      error = await response.json();
    } catch {}
    throw new Error(error.message || 'Có lỗi xảy ra');
  }
  return response.json();
};

// ========== STATISTICS API FUNCTIONS ==========

/**
 * Lấy thống kê phòng ban
 */
export const getDepartmentStats = async () => {
  try {
    const response = await fetch(STATISTICS_ENDPOINTS.DEPARTMENT_STATS, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return await handleResponse(response);
  } catch (error) {
    console.error('Error fetching department stats:', error);
    throw error;
  }
};

/**
 * Lấy thống kê người dùng
 */
export const getUserStats = async () => {
  try {
    const response = await fetch(STATISTICS_ENDPOINTS.USER_STATS, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return await handleResponse(response);
  } catch (error) {
    console.error('Error fetching user stats:', error);
    throw error;
  }
};

/**
 * Lấy thống kê dự án theo phòng ban
 */
export const getProjectsByDepartment = async () => {
  try {
    const response = await fetch(STATISTICS_ENDPOINTS.PROJECTS_BY_DEPARTMENT, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return await handleResponse(response);
  } catch (error) {
    console.error('Error fetching projects by department:', error);
    throw error;
  }
};

/**
 * Lấy thống kê dự án tất cả phòng ban
 */
export const getAllDepartmentsProjects = async () => {
  try {
    const response = await fetch(STATISTICS_ENDPOINTS.ALL_DEPARTMENTS_PROJECTS, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return await handleResponse(response);
  } catch (error) {
    console.error('Error fetching all departments projects:', error);
    throw error;
  }
};

/**
 * Lấy thống kê task được giao cho staff
 */
export const getStaffTaskAssignments = async () => {
  try {
    const response = await fetch(STATISTICS_ENDPOINTS.STAFF_TASK_ASSIGNMENTS, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return await handleResponse(response);
  } catch (error) {
    console.error('Error fetching staff task assignments:', error);
    throw error;
  }
};

/**
 * Lấy tổng hợp stats cho staff dashboard
 */
export const getStaffDashboard = async () => {
  try {
    const response = await fetch(STATISTICS_ENDPOINTS.STAFF_DASHBOARD, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return await handleResponse(response);
  } catch (error) {
    console.error('Error fetching staff dashboard:', error);
    throw error;
  }
};

/**
 * Lấy thống kê hệ thống (admin only)
 */
export const getSystemStats = async () => {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.SYSTEM_STATS, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return await handleResponse(response);
  } catch (error) {
    console.error('Error fetching system stats:', error);
    throw error;
  }
};

/**
 * Lấy dashboard admin (admin only)
 */
export const getAdminDashboard = async () => {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.DASHBOARD, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return await handleResponse(response);
  } catch (error) {
    console.error('Error fetching admin dashboard:', error);
    throw error;
  }
};

# Statistics API Integration

## Tổng quan

Tôi đã tích hợp các API thống kê từ backend vào component `StatisticsPlaceholder.jsx`. Dưới đây là chi tiết về các thay đổi và cách sử dụng.

## Các API Endpoints đã tích hợp

### 1. Statistics Endpoints
- `GET /api/stats/departments` - Thống kê phòng ban
- `GET /api/stats/users` - Thống kê người dùng  
- `GET /api/stats/projects-by-department` - Thống kê dự án theo phòng ban
- `GET /api/stats/all-departments-projects` - Thống kê dự án tất cả phòng ban
- `GET /api/stats/staff-task-assignments` - Thống kê task được giao cho staff
- `GET /api/stats/staff-dashboard` - Tổng hợp stats cho staff dashboard

### 2. Admin Endpoints
- `GET /api/admin/dashboard` - Dashboard admin
- `GET /api/admin/system-stats` - Thống kê hệ thống

## Files đã tạo/cập nhật

### 1. `src/api/statistics.js` (Mới)
File API chứa tất cả functions để gọi các endpoints thống kê:
- `getDepartmentStats()`
- `getUserStats()`
- `getProjectsByDepartment()`
- `getAllDepartmentsProjects()`
- `getStaffTaskAssignments()`
- `getStaffDashboard()`
- `getSystemStats()`
- `getAdminDashboard()`

### 2. `src/features/pages/Statistical/StatisticsPlaceholder.jsx` (Cập nhật)
Component chính đã được cập nhật để:
- Fetch data từ API thay vì sử dụng mock data
- Hiển thị loading states
- Xử lý error states
- Fallback về mock data nếu API không khả dụng

### 3. `src/test-statistics-api.js` (Mới)
File test để kiểm tra tất cả API endpoints.

## Cách sử dụng

### 1. Kiểm tra API
Mở browser console và chạy:
```javascript
testStatisticsAPI()
```

### 2. Cấu trúc dữ liệu mong đợi từ API

#### Department Stats Response:
```json
{
  "success": true,
  "data": [
    {
      "name": "Phòng IT",
      "totalEmployees": 12,
      "totalProjects": 50,
      "totalTasks": 24,
      "completionRate": 68
    }
  ]
}
```

#### Staff Dashboard Response:
```json
{
  "success": true,
  "data": {
    "totalEmployees": 12,
    "totalProjects": 50,
    "totalTasks": 24,
    "completionRate": 68,
    "monthlyCompletion": [
      {
        "month": "2025-01-01",
        "completionRate": 70
      },
      {
        "month": "2025-02-01", 
        "completionRate": 75
      }
    ]
  }
}
```

#### User Stats Response:
```json
{
  "success": true,
  "data": [
    {
      "id": "user_id",
      "fullName": "Nguyễn Văn A",
      "department": {
        "name": "Phòng IT"
      },
      "avatar": "avatar_url",
      "totalProjects": 4,
      "totalTasks": 10,
      "completionRate": 85,
      "status": "Đang triển khai",
      "statusData": {
        "labels": ["Hoàn thành", "Đang làm", "Chờ duyệt"],
        "data": [5, 3, 2],
        "colors": ["#66BB6A", "#42A5F5", "#FFB74D"]
      }
    }
  ]
}
```

## Features

### 1. Loading States
- Statistics cards hiển thị "..." khi đang tải
- Chart hiển thị "Đang tải dữ liệu biểu đồ..."
- Table hiển thị "Đang tải dữ liệu nhân sự..."

### 2. Error Handling
- Hiển thị thông báo lỗi nếu API call thất bại
- Fallback về mock data nếu cần thiết
- Console logging để debug

### 3. Data Transformation
- Chuyển đổi data từ API format sang format phù hợp với UI
- Tạo chart data từ monthly completion data
- Map user data cho employee table

### 4. Dynamic Departments
- Departments list được load từ API
- Fallback về danh sách cố định nếu API không có data

## Cách test

1. **Khởi động backend server** trên `http://localhost:3000`

2. **Đảm bảo có token hợp lệ** trong localStorage:
   - `token`
   - `authToken` 
   - hoặc `user.token`

3. **Mở trang Statistics** và kiểm tra:
   - Network tab để xem API calls
   - Console để xem logs
   - UI để xem data được hiển thị

4. **Chạy test function**:
   ```javascript
   // Trong browser console
   testStatisticsAPI().then(results => console.log(results))
   ```

## Lưu ý

- Component sẽ tự động fallback về mock data nếu API không khả dụng
- Tất cả API calls đều có error handling
- Loading states được hiển thị trong quá trình fetch data
- Data được refresh khi thay đổi department selection

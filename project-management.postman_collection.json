{"info": {"_postman_id": "fa13efe2-34ac-4d42-95a8-5a369fffc7f1", "name": "project-management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "40804583"}, "item": [{"name": "Admin", "item": [{"name": "auth admin", "item": [{"name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"Admin123!\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/forgot-password", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "forgot-password"]}}, "response": []}, {"name": "đặt lại mk", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"password\":\"Sanh123@\"\r\n\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/reset-password/55d0f390e5e9f7c7c9b69ff043a03a25c15f47ea76b215da0ed05591a37b6a39", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "reset-password", "55d0f390e5e9f7c7c9b69ff043a03a25c15f47ea76b215da0ed05591a37b6a39"]}}, "response": []}, {"name": "cậ<PERSON> nh<PERSON>t trang cá nhân", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.2H9L19Sbn9fdlx3TKzU-BWjv88lP0bE1bf6KgOjA_zE", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "\r\n   {\r\n  \"fullName\": \"<PERSON><PERSON> Tấn Sanh Update\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/profile", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "profile"]}}, "response": []}, {"name": "cậ<PERSON> nh<PERSON>t avatar", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.Unsbp2QyKgKLb1AH_OSQrO3Qfn6F7SywLjCD419Xtgc", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": [{"key": "avatar", "type": "file", "src": "postman-cloud:///1f0241a8-94b0-44b0-94f2-30c3f7bb9ba2"}]}, "url": {"raw": "http://localhost:3000/api/auth/update-avatar", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "update-avatar"]}}, "response": []}, {"name": "Đổi mk", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.6qYUlO48Hz909N7OWfUnTlY-pfv_7UdLGJvm8vYQn6Y", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\r\n  \"currentPassword\": \"Huynh123@@@\",\r\n  \"newPassword\": \"Huynh123@\",\r\n  \"confirmPassword\": \"Huynh123@\"\r\n\r\n}\r\n\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/change-password", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "change-password"]}}, "response": []}, {"name": "Profile", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.X4rJbUMF0X7kCbBro5bU3dFh5qMKR-MTCdeTCBTmE54", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost:3000/api/auth/profile", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "profile"]}}, "response": []}, {"name": "Profile id", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.X4rJbUMF0X7kCbBro5bU3dFh5qMKR-MTCdeTCBTmE54", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost:3000/api/auth/profile/687070664ab99ac8eabf3e68", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "profile", "687070664ab99ac8eabf3e68"]}}, "response": []}, {"name": "dashboard", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "dashboard"]}}, "response": []}, {"name": "b<PERSON>o trì", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"enabled\": false,\r\n  \"message\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/admin/maintenance/toggle", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "maintenance", "toggle"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> tra", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/maintenance/status", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "maintenance", "status"]}}, "response": []}, {"name": "thống kê hệ thống", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/system-stats", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "system-stats"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> động", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/activities", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "activities"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý phòng ban", "item": [{"name": "<PERSON><PERSON><PERSON> danh sách phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/departments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "departments"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Thực tập 3\",\r\n  \"code\": \"TT3\"\r\n  //\"headEmployeeCode\": \"NV002\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/departments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "departments"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> nhật phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Thực tập 3 Update\",\r\n  \"code\": \"TT3\",\r\n  \"headEmployeeCode\": \"NV004\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/departments/686f7ae627d52aadcb6c8cf8", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "departments", "686f7ae627d52aadcb6c8cf8"]}}, "response": []}, {"name": "xóa phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/departments/686f7ae627d52aadcb6c8cf8", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "departments", "686f7ae627d52aadcb6c8cf8"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng", "item": [{"name": "lấy ds user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.X4rJbUMF0X7kCbBro5bU3dFh5qMKR-MTCdeTCBTmE54", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> dùng", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.PhQfnT0Ee0ET_gX7hlWVFMqO1S8P1N_lFPrrIi3fYuU", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"fullName\": \"<PERSON><PERSON><PERSON>\",\r\n    \"email\": \"nam.v<PERSON><PERSON><PERSON><PERSON>@gmail.com\",\r\n    \"password\":\"Nam123!!!\",\r\n    \"role\": \"leader\",\r\n    \"position\": \"Backend\",\r\n    \"departmentCode\": \"TT1\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> dùng", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"fullName\": \"testupdate\",\r\n  \"role\": \"staff\",\r\n  \"position\": \"Test 1\"\r\n  \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> ho<PERSON> động của user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe/activities", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe", "activities"]}}, "response": []}, {"name": "<PERSON><PERSON> t<PERSON> hàng lo<PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"action\": \"block\",\r\n  \"userIds\": [\"686f9a4a664433e623c806fe\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/users/bulk", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "bulk"]}}, "response": []}, {"name": "Khóa/Mở Khóa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe/toggle-block", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe", "toggle-block"]}}, "response": []}, {"name": "Thay role User", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"newRole\": \"hr\",\r\n  \"reason\": \"Th<PERSON>ng chức\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe/change-role", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe", "change-role"]}}, "response": []}, {"name": "Xóa user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/686fce5d2d2e14696319d49b", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "686fce5d2d2e14696319d49b"]}}, "response": []}, {"name": "lấy ds user đã xóa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users/deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "deleted"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> phục người dùng", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe/restore", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe", "restore"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> v<PERSON>nh vi<PERSON><PERSON>(admin có thể khôi phục)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe/permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe", "permanent"]}}, "response": []}, {"name": "xem ds user x<PERSON><PERSON> v<PERSON><PERSON> v<PERSON><PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/users/permanently-deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "users", "permanently-deleted"]}}, "response": []}, {"name": "kh<PERSON>i phục user đã xóa vĩnh viễn", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/admin/users/686f9a4a664433e623c806fe/restore-permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "users", "686f9a4a664433e623c806fe", "restore-permanent"]}}, "response": []}, {"name": "lấy ds thành viên cũng phòng ban", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/department/6868a859ba2f43313f4e26b9/members", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "department", "6868a859ba2f43313f4e26b9", "members"]}}, "response": []}, {"name": "lấy ds thành viên cùng dự án", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/projects/686fec1862ead3ae888c0c87/members", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "projects", "686fec1862ead3ae888c0c87", "members"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý d<PERSON>n", "item": [{"name": "<PERSON><PERSON><PERSON> danh sách d<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eH7IMLmBLM-HEjLFt5CDJiB0wvDRIbmRzTrYkeXTngQ", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects"]}}, "response": []}, {"name": "l<PERSON>y chi tiết dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eH7IMLmBLM-HEjLFt5CDJiB0wvDRIbmRzTrYkeXTngQ", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/68917831c62466979f334600", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "68917831c62466979f334600"]}}, "response": []}, {"name": "tạo dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eH7IMLmBLM-HEjLFt5CDJiB0wvDRIbmRzTrYkeXTngQ", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Test7\",\r\n  \"description\": \"test7\",\r\n  \"departmentId\": \"687a57544e3a480b780b146e\",\r\n  \"leaderId\": \"68760dc2f870a5988ecf0eff\",\r\n  \"status\": \"waiting\",\r\n  \"priority\": \"high\",\r\n  \"startDate\": \"2025-08-05\",\r\n  \"endDate\": \"2025-08-31\",\r\n  \"members\": [\r\n    {\r\n      \"userId\": \"687bd18dc82ff91a7a513185\"\r\n    }\r\n  ],\r\n  \"followers\":\"[688a6648b2bbdb488a4b9f36]\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects"]}}, "response": []}, {"name": "sửa d<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n    {\r\n      \"userId\": \"687bd18dc82ff91a7a513185\"\r\n    }\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/6892cd1240e55363ab709240", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "6892cd1240e55363ab709240"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> d<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"reason\": \"Dự án bị hủy\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fe81eefc48f312ece3bfb", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fe81eefc48f312ece3bfb"]}}, "response": []}, {"name": "<PERSON>ó<PERSON> v<PERSON><PERSON> vi<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fe81eefc48f312ece3bfb/permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fe81eefc48f312ece3bfb", "permanent"]}}, "response": []}, {"name": "xem ds đã xóa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:3000/api/projects/deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "deleted"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> phục dự án đã xóa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fd0e7a3461d8d61e9bc12/restore", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fd0e7a3461d8d61e9bc12", "restore"]}}, "response": []}, {"name": "xem ds project xó<PERSON> v<PERSON><PERSON> vi<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/projects/permanently-deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "projects", "permanently-deleted"]}}, "response": []}, {"name": "khôi phục project đã xóa vĩnh viễn", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/admin/projects/686fe81eefc48f312ece3bfb/restore-permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "projects", "686fe81eefc48f312ece3bfb", "restore-permanent"]}}, "response": []}, {"name": "tải file", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": "postman-cloud:///1f05a348-bc4d-4fe0-90dd-c09012026f99"}]}, "url": {"raw": "http://localhost:3000/api/projects/686fe84eefc48f312ece3c22/upload", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fe84eefc48f312ece3c22", "upload"]}}, "response": []}, {"name": "Xem file", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fe84eefc48f312ece3c22/files", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fe84eefc48f312ece3c22", "files"]}}, "response": []}, {"name": "xóa file", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost:3000/api/projects/686fe84eefc48f312ece3c22/files/6870a9e9766d80861a6191d9", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fe84eefc48f312ece3c22", "files", "6870a9e9766d80861a6191d9"]}}, "response": []}, {"name": "thêm thành viên vào dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.afYaXIkViFNBiDLZLtuu43WML9S2aGa9bGertkRUHGY", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userId\": \"687bd18dc82ff91a7a513185\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/6892cd1240e55363ab709240/members", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "6892cd1240e55363ab709240", "members"]}}, "response": []}, {"name": "x<PERSON>a thành viên", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/members/686fce5d2d2e14696319d49b", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "members", "686fce5d2d2e14696319d49b"]}}, "response": []}, {"name": "lấy ds công việc trong dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.l0qS_1t_q45R8tMTJloF_O7pkFwUK0Mq4A_3oacBVBo", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/688ad167a5b4df27c9622c82/tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "688ad167a5b4df27c9622c82", "tasks"]}}, "response": []}, {"name": "tạo cv trong dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.K1caE4b1N0mqX9wi0y23kwEYGNkktypXfdV0k4_zu10", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"<PERSON> công việc có người theo dõi\",\r\n  \"description\": \"<PERSON><PERSON> tả công việc có người theo dõi\",\r\n  \"assignedToId\": \"687dfdd33604b0069c5efc8e\",\r\n  \"watchers\":\"686f9643b5a8083cb5fbad89\",\r\n  \"priority\": \"high\",\r\n  \"status\": \"pending\",\r\n  \"dueDate\": \"2025-08-04\",\r\n  \"startDate\": \"2025-08-05\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/688ad167a5b4df27c9622c82/tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "688ad167a5b4df27c9622c82", "tasks"]}}, "response": []}, {"name": "cậ<PERSON> nh<PERSON>t tiến độ cv", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"status\": \"in_progress\",\r\n  \"progress\": 50,\r\n  \"description\": \"<PERSON><PERSON><PERSON> nhật mô tả công việc\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/686fed6662ead3ae888c0cbf", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "686fed6662ead3ae888c0cbf"]}}, "response": []}, {"name": "b<PERSON><PERSON> lu<PERSON> task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{ \"content\": \"<PERSON><PERSON> hoàn thành 100% công việc này\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708fd3036b9c5a2280410c/comments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708fd3036b9c5a2280410c", "comments"]}}, "response": []}, {"name": "l<PERSON>y ds bình luận", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708fd3036b9c5a2280410c/comments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708fd3036b9c5a2280410c", "comments"]}}, "response": []}, {"name": "x<PERSON><PERSON> bl", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708fd3036b9c5a2280410c/comments/68708ffd036b9c5a2280411e", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708fd3036b9c5a2280410c", "comments", "68708ffd036b9c5a2280411e"]}}, "response": []}, {"name": "xóa task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708dcbbd222034828f4c18", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708dcbbd222034828f4c18"]}}, "response": []}, {"name": "l<PERSON><PERSON> danh s<PERSON>ch task đã xóa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "deleted"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> phụ<PERSON> task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708dcbbd222034828f4c18/restore", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708dcbbd222034828f4c18", "restore"]}}, "response": []}, {"name": "xóa vv task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708dcbbd222034828f4c18/permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708dcbbd222034828f4c18", "permanent"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> kê <PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/tasks/686fed6662ead3ae888c0cbf/stats/overview", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", "686fed6662ead3ae888c0cbf", "stats", "overview"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý công vi<PERSON>c cá nhân", "item": [{"name": "Tạo cv", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"Công việc 1\",\r\n  \"description\": \"test 1\"\r\n  \r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks"]}}, "response": []}, {"name": "lấy cv", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks"]}}, "response": []}, {"name": "l<PERSON>y cv chi tiết", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/68706e274ab99ac8eabf3e2d", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "68706e274ab99ac8eabf3e2d"]}}, "response": []}, {"name": "sửa cv", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n    \"title\":\"Update\",\r\n  \"description\": \"<PERSON><PERSON>n thành báo cáo tháng - Updated\"\r\n\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/68706e274ab99ac8eabf3e2d", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "68706e274ab99ac8eabf3e2d"]}}, "response": []}, {"name": "Xóa cv cá nhân vào thùng rác", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/68706e274ab99ac8eabf3e2d", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "68706e274ab99ac8eabf3e2d"]}}, "response": []}, {"name": "xem ds thùng r<PERSON>c", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/common/personal-tasks/trashed", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "trashed"]}}, "response": []}, {"name": "kh<PERSON><PERSON> phục", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************.Ek1RN1-usrKnVyQ-8BiEy4VUpBEuWbOtUfn-2BfcTqU", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "http://localhost:3000/api/staff/personal-tasks/6868bac3d6aff09c1d5d01c8/restore", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "staff", "personal-tasks", "6868bac3d6aff09c1d5d01c8", "restore"]}}, "response": []}, {"name": "<PERSON>ó<PERSON> v<PERSON><PERSON> vi<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/68706e274ab99ac8eabf3e2d/permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "68706e274ab99ac8eabf3e2d", "permanent"]}}, "response": []}]}, {"name": "qu<PERSON><PERSON> lý email", "item": [{"name": "trạng thái email", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/email-status", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "email-status"]}}, "response": []}, {"name": "retry thủ công", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "http://localhost:3000/api/admin/retry-emails", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "retry-emails"]}}, "response": []}, {"name": "d<PERSON><PERSON> log email", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/admin/clean-email-logs", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "clean-email-logs"]}}, "response": []}]}, {"name": "t<PERSON><PERSON> k<PERSON>m", "item": [{"name": "tìm kiếm với từ khóa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/search?query=công việc", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "search"], "query": [{"key": "query", "value": "c<PERSON><PERSON> vi<PERSON>c"}]}}, "response": []}, {"name": "l<PERSON><PERSON> sử tìm kiếm", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/search/history", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "search", "history"]}}, "response": []}, {"name": "tìm kiếm nâng cao", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"query\": \"test\",\r\n  \"filters\": {\r\n    \"type\": \"users\",\r\n    \"departmentId\": \"6868a859ba2f43313f4e26b9\" \r\n  },\r\n  \"limit\": 10,\r\n  \"page\": 1,\r\n  \"sortBy\": \"relevance\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/common/search/advanced", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "search", "advanced"]}}, "response": []}, {"name": "x<PERSON><PERSON> lịch sử tìm kiếm", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/search/history", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "search", "history"]}}, "response": []}]}, {"name": "thống kê", "item": [{"name": "<PERSON>h<PERSON><PERSON> kê phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O6ShiVN7DnSnZNXdg82edPgr50OAKxYkQSscQ9j8Sk4", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/departments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "departments"]}}, "response": []}, {"name": "<PERSON>h<PERSON><PERSON> kê người dùng", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/users", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "users"]}}, "response": []}, {"name": "Thống kê project phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O6ShiVN7DnSnZNXdg82edPgr50OAKxYkQSscQ9j8Sk4", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/projects-by-department", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "projects-by-department"]}}, "response": []}, {"name": "Thống kê project tất cả phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O6ShiVN7DnSnZNXdg82edPgr50OAKxYkQSscQ9j8Sk4", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/all-departments-projects", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "all-departments-projects"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> kê <PERSON> đ<PERSON><PERSON><PERSON> giao", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O6ShiVN7DnSnZNXdg82edPgr50OAKxYkQSscQ9j8Sk4", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/staff-task-assignments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "staff-task-assignments"]}}, "response": []}, {"name": "thống kê người dùng", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O6ShiVN7DnSnZNXdg82edPgr50OAKxYkQSscQ9j8Sk4", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users"]}}, "response": []}, {"name": "tổng hợp stats cho staff", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9cuGnkmagSOHiPl_f5VeBc97bt853pqxnSftTgAJhic", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/staff-dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "staff-dashboard"]}}, "response": []}]}, {"name": "thông báo", "item": [{"name": "all thông báo", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.r2ubeW2TcuvxNbgkA0pzcWZMocqHguGXM9QYskCnQxU", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/notification", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification"]}}, "response": []}, {"name": "thông b<PERSON>o ch<PERSON><PERSON> đ<PERSON>c", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9r2z1TsFElNu6VS27465hroBkC97RbsZHOHfzgVjy_s", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/notification/unread-count", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "unread-count"]}}, "response": []}, {"name": "thông báo theo lo<PERSON>i", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MPUV1sfZG_D4M5gcaLwLXWNp10M7x56URbNY38iIx1I", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "// \"task_assigned\",\r\n//     \"task_completed\",\r\n//     \"task_overdue\",\r\n//     \"project_created\",\r\n//     \"project_updated\",\r\n//     \"project_completed\",\r\n//     \"system\",\r\n//     \"reminder\",\r\n//     \"announcement\",\r\n//     \"other\"", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/notification/type/task_completed", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "type", "task_completed"]}}, "response": []}, {"name": "đ<PERSON><PERSON> dấu đã đọc", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/notification/mark-read/68709cd0a34e47cafe63f03e", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "mark-read", "68709cd0a34e47cafe63f03e"]}}, "response": []}, {"name": "đ<PERSON><PERSON> dấu đã đọc tất cả", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9r2z1TsFElNu6VS27465hroBkC97RbsZHOHfzgVjy_s", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/notification/mark-all-read", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "mark-all-read"]}}, "response": []}, {"name": "xóa all thông báo đã đọc", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/notification/delete-read/all", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "delete-read", "all"]}}, "response": []}, {"name": "x<PERSON>a thông b<PERSON><PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/notification/68709cd0a34e47cafe63f03e", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "68709cd0a34e47cafe63f03e"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> dấu ch<PERSON>a đ<PERSON><PERSON> tất cả", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9r2z1TsFElNu6VS27465hroBkC97RbsZHOHfzgVjy_s", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/notification/mark-all-unread", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "mark-all-unread"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON> việc trong dự án", "item": [{"name": "tạo dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O6ShiVN7DnSnZNXdg82edPgr50OAKxYkQSscQ9j8Sk4", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Test 07-08-2025\",\r\n  \"description\": \"Nam test\",\r\n  \"departmentId\": \"687a57544e3a480b780b146e\",\r\n  \"leaderId\": \"687baa5ea0aeae46fd6a4581\",\r\n  \"status\": \"waiting\",\r\n  \"priority\": \"high\",\r\n  \"startDate\": \"2025-08-07\",\r\n  \"endDate\": \"2025-08-15\",\r\n  \"members\": [\r\n    {\r\n      \"userId\": \"687dfdd33604b0069c5efc8e\"\r\n    }\r\n  ],\r\n  \"followers\":\"[68709cd0a34e47cafe63f03a]\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> danh sách d<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6ZnABa1UpbQMmDz7K65JNx6HjgSsewR_-75q1TuefWk", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects"]}}, "response": []}, {"name": "l<PERSON>y chi tiết dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6ZnABa1UpbQMmDz7K65JNx6HjgSsewR_-75q1TuefWk", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/6892cd1240e55363ab709240", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "6892cd1240e55363ab709240"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> bình thường không lặp lại", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.afYaXIkViFNBiDLZLtuu43WML9S2aGa9bGertkRUHGY", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"<PERSON><PERSON><PERSON> tra thông báo cho <PERSON>\",\r\n  \"description\": \"Kiểm tra và test code trước khi deploy\",\r\n  \"assignedToIds\": [\"687dfdd33604b0069c5efc8e\",\"687bd18dc82ff91a7a513185\"],  //id thà<PERSON> viên\r\n  \"watchers\":  [\"687757cea07c78b0612a010d\", \"68709cd0a34e47cafe63f03a\"], //id ng theo dõi\r\n  \"priority\": \"high\",\r\n  \"startDate\": \"2025-08-06\",\r\n  \"dueDate\": \"2025-08-08\",\r\n  \"isRepeat\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/6892cd1240e55363ab709240/tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "6892cd1240e55363ab709240", "tasks"]}}, "response": []}, {"name": "Tạo Task Lặp Lại Hằng <PERSON> (thứ 2-chủ nhật)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O6ShiVN7DnSnZNXdg82edPgr50OAKxYkQSscQ9j8Sk4", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"07-08-2025 nam kiểm tra\",\r\n  \"description\": \"Kiểm tra \",\r\n  \"assignedToId\": \"687dfdd33604b0069c5efc8e\", //id thành viên\r\n  \"watchers\":  [\"687757cea07c78b0612a010d\", \"68709cd0a34e47cafe63f03a\"], //id ng theo dõi\r\n  \"priority\": \"medium\",\r\n  \"startDate\": \"2025-08-07\",\r\n  \"dueDate\": \"2025-08-15\",\r\n  \"isRepeat\": true,\r\n  \"repeatType\": \"daily\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/6894126706bf0b229fb92930/tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "6894126706bf0b229fb92930", "tasks"]}}, "response": []}, {"name": "Tạo Task Lặp Lại Hằng Tuần tùy chọn ngày (thứ 2-chủ nhật)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O6ShiVN7DnSnZNXdg82edPgr50OAKxYkQSscQ9j8Sk4", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"<PERSON><PERSON><PERSON> cuối tuần\",\r\n  \"description\": \"<PERSON>ọ<PERSON> phòng ban\",\r\n  \"assignedToId\": \"687dfdd33604b0069c5efc8e\", //id thành viên\r\n  \"priority\": \"medium\", \r\n  \"watchers\": [\"687757cea07c78b0612a010d\", \"68709cd0a34e47cafe63f03a\"], //id ng theo dõi\r\n  \"startDate\": \"2025-08-07\",\r\n  \"dueDate\": \"2025-08-14\",\r\n  \"isRepeat\": true,\r\n  \"repeatType\": \"weekly\",\r\n  \"repeatDays\": [5,6], // Thứ 6, 7\r\n  \"repeatEndDate\": \"2025-08-15\" //kết thúc ngày lặp lại\r\n}\r\n\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/6894126706bf0b229fb92930/tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "6894126706bf0b229fb92930", "tasks"]}}, "response": []}, {"name": "Tạo Task Lặp Lại Hằng Tuần Với Số Lần G<PERSON>ớ<PERSON> (phát triển thêm)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cK3DIkJ2EmeP89WkVpRobUMXjj95LSGKEV-PlxUE9Fc", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"Báo cáo tuần\",\r\n  \"description\": \"<PERSON><PERSON><PERSON> báo cáo công việc tuần\",\r\n  \"assignedToId\": \"687dfdd33604b0069c5efc8e\", \r\n  \"priority\": \"high\",\r\n  \"status\":\"in_progress\",\r\n  \"watchers\": \"687af91b4cf29d8218350d47\",\r\n  \"startDate\": \"2025-08-05\",\r\n  \"dueDate\": \"2025-08-11\",\r\n  \"isRepeat\": true,\r\n  \"repeatType\": \"weekly\",\r\n  \"repeatDays\": [1], // Chỉ Thứ 2\r\n  \"repeatCount\": 3 // Lặp 3 lần\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/68917831c62466979f334600/tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "68917831c62466979f334600", "tasks"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> danh s<PERSON>ch ng<PERSON><PERSON>i theo dõi task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/tasks/689066e38e72e3e292eb8b4b/watchers", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", "689066e38e72e3e292eb8b4b", "watchers"]}}, "response": []}, {"name": "thêm/x<PERSON>a bản thân khỏi người theo dõi task mà mình tạo", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4N2FmOTFiNGNmMjlkODIxODM1MGQ0NyIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoibmFtdnUyMjExMTk5OUBnbWFpbC5jb20iLCJkZXBhcnRtZW50SWQiOiI2ODdhNTc1NDRlM2E0ODBiNzgwYjE0NmUiLCJzZXNzaW9uSWQiOiI4YmFjYWVjY2QyOGI3OGRhMTg0YzBmNmJjZTdhNzZhZiIsImlhdCI6MTc1NDI5Mzc4NiwiZXhwIjoxNzU0MzIyNTg2LCJhdWQiOiJhZG1pbi1wYW5lbCIsImlzcyI6InByb2plY3QtbWFuYWdlbWVudC1hcGkifQ.fWCvAo_M_bIIBDjQ9zYOcGrkagZ5f7na74vtpW4rFF4", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/tasks/689066e38e72e3e292eb8b4b/toggle-watch", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", "689066e38e72e3e292eb8b4b", "toggle-watch"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> danh s<PERSON>ch task mà user đang theo dõi", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4N2FmOTFiNGNmMjlkODIxODM1MGQ0NyIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoibmFtdnUyMjExMTk5OUBnbWFpbC5jb20iLCJkZXBhcnRtZW50SWQiOiI2ODdhNTc1NDRlM2E0ODBiNzgwYjE0NmUiLCJzZXNzaW9uSWQiOiJlNWQ2MjJiZTMyYmM4ODkwMzEzMjNiOGZmZTUwZWFkYyIsImlhdCI6MTc1NDI5ODUyOSwiZXhwIjoxNzU0MzI3MzI5LCJhdWQiOiJhZG1pbi1wYW5lbCIsImlzcyI6InByb2plY3QtbWFuYWdlbWVudC1hcGkifQ.50mV4-8efNkfujZvLSId437q44IXbeVLbl4vkTiKyJc", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/tasks/watched", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", "watched"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>i theo dõi task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4N2FmOTFiNGNmMjlkODIxODM1MGQ0NyIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoibmFtdnUyMjExMTk5OUBnbWFpbC5jb20iLCJkZXBhcnRtZW50SWQiOiI2ODdhNTc1NDRlM2E0ODBiNzgwYjE0NmUiLCJzZXNzaW9uSWQiOiI4YmFjYWVjY2QyOGI3OGRhMTg0YzBmNmJjZTdhNzZhZiIsImlhdCI6MTc1NDI5Mzc4NiwiZXhwIjoxNzU0MzIyNTg2LCJhdWQiOiJhZG1pbi1wYW5lbCIsImlzcyI6InByb2plY3QtbWFuYWdlbWVudC1hcGkifQ.fWCvAo_M_bIIBDjQ9zYOcGrkagZ5f7na74vtpW4rFF4", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/tasks/:id/watchers/:userId", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", ":id", "watchers", ":userId"], "variable": [{"key": "id", "value": "689066e38e72e3e292eb8b4b"}, {"key": "userId", "value": "687af91b4cf29d8218350d47"}]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON><PERSON> theo dõi task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4N2FmOTFiNGNmMjlkODIxODM1MGQ0NyIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoibmFtdnUyMjExMTk5OUBnbWFpbC5jb20iLCJkZXBhcnRtZW50SWQiOiI2ODdhNTc1NDRlM2E0ODBiNzgwYjE0NmUiLCJzZXNzaW9uSWQiOiI4YmFjYWVjY2QyOGI3OGRhMTg0YzBmNmJjZTdhNzZhZiIsImlhdCI6MTc1NDI5Mzc4NiwiZXhwIjoxNzU0MzIyNTg2LCJhdWQiOiJhZG1pbi1wYW5lbCIsImlzcyI6InByb2plY3QtbWFuYWdlbWVudC1hcGkifQ.fWCvAo_M_bIIBDjQ9zYOcGrkagZ5f7na74vtpW4rFF4", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userId\": \"687af91b4cf29d8218350d47\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/tasks/689066e38e72e3e292eb8b4b/watchers", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", "689066e38e72e3e292eb8b4b", "watchers"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> nhật Task (thay đổi ngày lặp lại-timeline)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O6ShiVN7DnSnZNXdg82edPgr50OAKxYkQSscQ9j8Sk4", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "// {\r\n//   \"repeatType\": \"weekly\",\r\n//   \"repeatDays\": [1, 2, 3], // Th<PERSON> 2, 3, 4\r\n//   \"isRepeat\":\"true\"\r\n// }\r\n\r\n// {\r\n//   \"title\": \"<PERSON>ông việc quá sớm\",\r\n//   \"startDate\": \"2023-12-01\",  // ❌ Trước ngày bắt đầu dự án\r\n//   \"dueDate\": \"2024-01-15\"\r\n// }\r\n\r\n// {\r\n//   \"title\": \"Công việc quá muộn\",\r\n//   \"startDate\": \"2025-08-05\",\r\n//   \"dueDate\": \"2025-09-15\"     // ❌ <PERSON><PERSON> ngày kết thúc dự án\r\n// }\r\n\r\n{\r\n    \"startDate\": \"2025-08-07\",\r\n    \"dueDate\": \"2025-08-14\"\r\n}\r\n\r\n"}, "url": {"raw": "http://localhost:3000/api/projects/6894126706bf0b229fb92930/tasks/6894141e06bf0b229fb92bff", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "6894126706bf0b229fb92930", "tasks", "6894141e06bf0b229fb92bff"]}}, "response": []}, {"name": "Tắt lặp lại", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4N2FmOTFiNGNmMjlkODIxODM1MGQ0NyIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoibmFtdnUyMjExMTk5OUBnbWFpbC5jb20iLCJkZXBhcnRtZW50SWQiOiI2ODdhNTc1NDRlM2E0ODBiNzgwYjE0NmUiLCJzZXNzaW9uSWQiOiIwYThmYTRhM2U3Mjk3NmMzNGZhMWUwYjliMGFmYjJjNCIsImlhdCI6MTc1NDM2NTk0MCwiZXhwIjoxNzU0Mzk0NzQwLCJhdWQiOiJhZG1pbi1wYW5lbCIsImlzcyI6InByb2plY3QtbWFuYWdlbWVudC1hcGkifQ.K899lcUyulvfUiQN2-RFSSofb9AU8GgaQG_R9hrcIhE", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"isRepeat\": false\r\n}"}, "url": {"raw": "http://localhost:3000/api/projects/68917831c62466979f334600/tasks/68917dc7c62466979f33465f", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "68917831c62466979f334600", "tasks", "68917dc7c62466979f33465f"]}}, "response": []}, {"name": "cậ<PERSON> nh<PERSON>t tiến độ cv", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4OTFhYWNkODM5NDZmYzE5NmNjNzU5NiIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoidnVwaGFuaG9haW5hbUBnbWFpbC5jb20iLCJkZXBhcnRtZW50SWQiOiI2ODhhZDM0NmE1YjRkZjI3Yzk2MjJmOGYiLCJzZXNzaW9uSWQiOiI1NGRmNzliOTFmYjM5NDNmYzZjMWViZmMxYmFiOTc5MiIsImlhdCI6MTc1NDQ2MjY1MiwiZXhwIjoxNzU0NDkxNDUyLCJhdWQiOiJhZG1pbi1wYW5lbCIsImlzcyI6InByb2plY3QtbWFuYWdlbWVudC1hcGkifQ.7z5LpD-iOMjcLMEbHaM8Cwn9kJYbULRFmktQsJm8jtI", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"progress\": 50,\r\n  \"description\": \"<PERSON><PERSON><PERSON> nhật họp tuần\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/6892cd1240e55363ab709240/tasks/6892fa07b9498bfc45bd2a88", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "6892cd1240e55363ab709240", "tasks", "6892fa07b9498bfc45bd2a88"]}}, "response": []}, {"name": "<PERSON><PERSON>y ds task (admin-ceo)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4OTFhYWNkODM5NDZmYzE5NmNjNzU5NiIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoidnVwaGFuaG9haW5hbUBnbWFpbC5jb20iLCJkZXBhcnRtZW50SWQiOiI2ODhhZDM0NmE1YjRkZjI3Yzk2MjJmOGYiLCJzZXNzaW9uSWQiOiI1NGRmNzliOTFmYjM5NDNmYzZjMWViZmMxYmFiOTc5MiIsImlhdCI6MTc1NDQ2MjY1MiwiZXhwIjoxNzU0NDkxNDUyLCJhdWQiOiJhZG1pbi1wYW5lbCIsImlzcyI6InByb2plY3QtbWFuYWdlbWVudC1hcGkifQ.7z5LpD-iOMjcLMEbHaM8Cwn9kJYbULRFmktQsJm8jtI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "tasks"]}}, "response": []}, {"name": "Tải file công việc", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4N2FmOTFiNGNmMjlkODIxODM1MGQ0NyIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoibmFtdnUyMjExMTk5OUBnbWFpbC5jb20iLCJkZXBhcnRtZW50SWQiOiI2ODdhNTc1NDRlM2E0ODBiNzgwYjE0NmUiLCJzZXNzaW9uSWQiOiJhNjY2NWY3OGFhMTE2NWEyYzU0ZjgxZTk5NTA1OTFlNCIsImlhdCI6MTc1NDM2ODY5MiwiZXhwIjoxNzU0Mzk3NDkyLCJhdWQiOiJhZG1pbi1wYW5lbCIsImlzcyI6InByb2plY3QtbWFuYWdlbWVudC1hcGkifQ.Uzdeh1cSlobK9IHwBfl4miJvINglU9q5BPF25QYY7is", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/D:/Hình ảnh/Ảnh chụp màn hình/diagram.png"}]}, "url": {"raw": "http://localhost:3000/api/projects/68917831c62466979f334600/tasks/6891861433b8f0283554186c/attachments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "68917831c62466979f334600", "tasks", "6891861433b8f0283554186c", "attachments"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> chi tiết task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4OTFhYWNkODM5NDZmYzE5NmNjNzU5NiIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoidnVwaGFuaG9haW5hbUBnbWFpbC5jb20iLCJkZXBhcnRtZW50SWQiOiI2ODhhZDM0NmE1YjRkZjI3Yzk2MjJmOGYiLCJzZXNzaW9uSWQiOiJiZjM1ZTAxYWMxYmZiZTk2YjFkODMxM2Y4YWI2YzdkNSIsImlhdCI6MTc1NDM4MDM0NywiZXhwIjoxNzU0NDA5MTQ3LCJhdWQiOiJhZG1pbi1wYW5lbCIsImlzcyI6InByb2plY3QtbWFuYWdlbWVudC1hcGkifQ.cnFa3Bi0OyB8sZKWTJeiN_TUhzjhf7GfUyu8_z75PAA", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/68917831c62466979f334600/tasks/68917dc7c62466979f33465f", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "68917831c62466979f334600", "tasks", "68917dc7c62466979f33465f"]}}, "response": []}]}, {"name": "login ng theo dõi", "item": [{"name": "<PERSON><PERSON><PERSON>p", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"Huynh123!\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "response": []}, {"name": "all thông báo", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.U050HL2sycydGpgBa5xJfJPumgChfe71oCtBjwxpI4A", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/notification", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification"]}}, "response": []}, {"name": "đ<PERSON><PERSON> ng theo dõi", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.afYaXIkViFNBiDLZLtuu43WML9S2aGa9bGertkRUHGY", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"watchers\": [\"687757cea07c78b0612a010d\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/6892cd1240e55363ab709240/tasks/6892d2ce40e55363ab709467", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "6892cd1240e55363ab709240", "tasks", "6892d2ce40e55363ab709467"]}}, "response": []}]}, {"name": "test riêng", "item": [{"name": "<PERSON><PERSON><PERSON>p", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"nam.v<PERSON><PERSON><PERSON><PERSON>@gmail.com\",\r\n    \"password\": \"Nam123!!!\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "response": []}, {"name": "l<PERSON>y thông báo lặp lại", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************.zJkoOXfMOw8MN6hbzRmoLTSMdSMm9IPdUuLo-NUUCnk", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/notification", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification"]}}, "response": []}]}]}, {"name": "<PERSON><PERSON><PERSON>p", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"vup<PERSON><PERSON><PERSON><PERSON>@gmail.com\",\r\n    \"password\": \"Nam123!!!\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"sanh<PERSON><PERSON><PERSON><PERSON>@gmail.com\",\r\n    \"password\": \"Hts12345@\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "response": []}]}
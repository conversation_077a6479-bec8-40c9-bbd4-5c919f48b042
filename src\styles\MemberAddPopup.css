@import url('../index.css');

.member-add-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1002;
}

.member-add-popup {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  width: 450px;
  max-width: 90vw;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  position: relative;
}

.member-add-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.member-add-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.member-add-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.member-add-close-btn:hover {
  color: #666;
  background: #f5f5f5;
}

.member-add-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.member-add-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.member-add-form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.member-add-input-container {
  position: relative;
}

.member-add-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  color: #333;
  background: #fff;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.member-add-input:focus {
  outline: none;
  border-color: #007bff;
}

.member-add-input.error {
  border-color: #ff4757;
}

.member-add-input::placeholder {
  color: #999;
}

.member-add-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.member-add-suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f5f5f5;
}

.member-add-suggestion-item:hover {
  background: #f8f9ff;
}

.member-add-suggestion-item:last-child {
  border-bottom: none;
}

/* Styling cho thành viên từ công việc chính */
.member-add-suggestion-item.parent-task-member {
  background: #f0f8ff;
  border-left: 3px solid #1890ff;
}

.member-add-suggestion-item.parent-task-member:hover {
  background: #e6f7ff;
}

.member-add-suggestion-item.parent-task-member .suggestion-name {
  color: #1890ff;
  font-weight: 600;
}

.suggestion-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e0e0e0;
  flex-shrink: 0;
}

.suggestion-info {
  flex: 1;
  min-width: 0;
}

.suggestion-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.suggestion-email {
  font-size: 13px;
  color: #666;
  margin-bottom: 2px;
}

.suggestion-department {
  font-size: 12px;
  color: #999;
}

.member-add-error {
  color: #ff4757;
  font-size: 13px;
  margin-top: 4px;
}

.member-add-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 8px;
}

.member-add-cancel-btn,
.member-add-submit-btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.member-add-cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.member-add-cancel-btn:hover {
  background: #e0e0e0;
}

.member-add-submit-btn {
  background: #007bff;
  color: #fff;
}

.member-add-submit-btn:hover {
  background: #0056b3;
}

.member-add-submit-btn:active,
.member-add-cancel-btn:active {
  transform: translateY(1px);
}

/* Loading styles */
.member-add-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  color: #666;
  font-size: 13px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 8px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Disabled state */
.member-add-input:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.member-add-submit-btn:disabled {
  background: #ccc;
  color: #999;
  cursor: not-allowed;
}

.member-add-submit-btn:disabled:hover {
  background: #ccc;
}

/* Scrollbar styling cho suggestions */
.member-add-suggestions::-webkit-scrollbar {
  width: 6px;
}

.member-add-suggestions::-webkit-scrollbar-track {
  background: transparent;
}

.member-add-suggestions::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
}

.member-add-suggestions::-webkit-scrollbar-thumb:hover {
  background: #bbb;
}

/* Responsive */
@media (max-width: 768px) {
  .member-add-popup {
    width: 100%;
    margin: 0 16px;
    padding: 20px;
  }
  
  .member-add-actions {
    flex-direction: column;
  }
  
  .member-add-cancel-btn,
  .member-add-submit-btn {
    width: 100%;
  }
}
